import { contextVaultService } from './contextVaultService'

export interface SharedDropboxFile {
  id: string
  filename: string
  filepath: string
  fileType: string
  fileSize: number
  uploadedAt: string
  processed: boolean
  extractedContent?: string
}

export interface UploadDestination {
  type: 'context' | 'shared'
  contextId?: string
  contextName?: string
  path: string
}

class SharedDropboxService {
  private readonly SHARED_FOLDER_NAME = 'shared-dropbox'
  private files: SharedDropboxFile[] = []
  private listeners: ((files: SharedDropboxFile[]) => void)[] = []

  /**
   * Initialize shared dropbox - create folder structure if needed
   */
  async initialize(): Promise<void> {
    try {
      const vaultRoot = await this.getVaultRoot()
      if (!vaultRoot) {
        console.log('[SharedDropbox] No vault root configured, skipping initialization')
        return
      }

      const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
      
      // Create shared dropbox directory
      const createResult = await window.electronAPI.vault.createDirectory(sharedPath)
      if (!createResult.success) {
        console.error('[SharedDropbox] Failed to create shared directory:', createResult.error)
        return
      }

      // Create metadata file if it doesn't exist
      const metadataPath = `${sharedPath}/.metadata.json`
      const metadataExists = await window.electronAPI.vault.pathExists(metadataPath)
      
      if (!metadataExists.exists) {
        const metadata = {
          name: 'Shared Dropbox',
          description: 'Files uploaded when no context vault is selected',
          created: new Date().toISOString(),
          type: 'shared-dropbox'
        }
        
        await window.electronAPI.vault.writeFile(metadataPath, JSON.stringify(metadata, null, 2))
      }

      // Load existing files
      await this.loadFiles()
      
      console.log('[SharedDropbox] Initialized successfully')
    } catch (error) {
      console.error('[SharedDropbox] Initialization failed:', error)
    }
  }

  /**
   * Get vault root path
   */
  private async getVaultRoot(): Promise<string | null> {
    try {
      const registry = await window.electronAPI.vault.getVaultRegistry()
      return registry?.vaultRoot || null
    } catch (error) {
      console.error('[SharedDropbox] Failed to get vault root:', error)
      return null
    }
  }

  /**
   * Determine upload destination based on current context selection
   */
  async getUploadDestination(): Promise<UploadDestination> {
    const selectedContext = contextVaultService.getSelectedContext()

    if (selectedContext) {
      // Context vault selected - upload to context's actual path
      const contextPath = selectedContext.path

      return {
        type: 'context',
        contextId: selectedContext.id,
        contextName: selectedContext.name,
        path: contextPath
      }
    } else {
      // No context selected - upload to shared dropbox
      const vaultRoot = await this.getVaultRoot()
      const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`

      return {
        type: 'shared',
        path: sharedPath
      }
    }
  }

  /**
   * Upload file to determined destination
   */
  async uploadFile(file: File): Promise<{ success: boolean; fileRecord?: SharedDropboxFile; error?: string }> {
    try {
      const destination = await this.getUploadDestination()
      
      // Generate unique filename
      const timestamp = Date.now()
      const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const filename = `${timestamp}_${sanitizedName}`
      const filepath = `${destination.path}/${filename}`

      // Convert file to base64 for transfer
      const arrayBuffer = await file.arrayBuffer()
      const base64Content = Buffer.from(arrayBuffer).toString('base64')

      // Write file to destination
      const writeResult = await window.electronAPI.vault.writeFile(filepath, base64Content)
      if (!writeResult.success) {
        return { success: false, error: writeResult.error }
      }

      // Create file record
      const fileRecord: SharedDropboxFile = {
        id: `${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
        filename: file.name,
        filepath,
        fileType: file.type || 'unknown',
        fileSize: file.size,
        uploadedAt: new Date().toISOString(),
        processed: false
      }

      // If uploaded to shared dropbox, track it
      if (destination.type === 'shared') {
        this.files.push(fileRecord)
        this.notifyListeners()
        
        // Save updated file list
        await this.saveFileList()
      }

      console.log(`[SharedDropbox] File uploaded to ${destination.type}:`, {
        filename: file.name,
        destination: destination.path,
        contextName: destination.contextName
      })

      return { success: true, fileRecord }
    } catch (error) {
      console.error('[SharedDropbox] Upload failed:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Load files from shared dropbox
   */
  private async loadFiles(): Promise<void> {
    try {
      const vaultRoot = await this.getVaultRoot()
      if (!vaultRoot) return

      const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
      const filesListPath = `${sharedPath}/.files.json`
      
      const fileExists = await window.electronAPI.vault.pathExists(filesListPath)
      if (!fileExists.exists) {
        this.files = []
        return
      }

      const readResult = await window.electronAPI.vault.readFile(filesListPath)
      if (readResult.success && readResult.content) {
        this.files = JSON.parse(readResult.content)
      }
    } catch (error) {
      console.error('[SharedDropbox] Failed to load files:', error)
      this.files = []
    }
  }

  /**
   * Save file list to shared dropbox
   */
  private async saveFileList(): Promise<void> {
    try {
      const vaultRoot = await this.getVaultRoot()
      if (!vaultRoot) return

      const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
      const filesListPath = `${sharedPath}/.files.json`
      
      await window.electronAPI.vault.writeFile(filesListPath, JSON.stringify(this.files, null, 2))
    } catch (error) {
      console.error('[SharedDropbox] Failed to save file list:', error)
    }
  }

  /**
   * Get all shared dropbox files
   */
  getFiles(): SharedDropboxFile[] {
    return [...this.files]
  }

  /**
   * Subscribe to file changes
   */
  subscribe(listener: (files: SharedDropboxFile[]) => void): () => void {
    this.listeners.push(listener)
    listener(this.files) // Immediate call with current data
    
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.files))
  }

  /**
   * Remove file from shared dropbox
   */
  async removeFile(fileId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const fileIndex = this.files.findIndex(f => f.id === fileId)
      if (fileIndex === -1) {
        return { success: false, error: 'File not found' }
      }

      const file = this.files[fileIndex]
      
      // Remove physical file
      const removeResult = await window.electronAPI.vault.removeFile(file.filepath)
      if (!removeResult.success) {
        return { success: false, error: removeResult.error }
      }

      // Remove from tracking
      this.files.splice(fileIndex, 1)
      this.notifyListeners()
      
      // Save updated list
      await this.saveFileList()

      return { success: true }
    } catch (error) {
      console.error('[SharedDropbox] Failed to remove file:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get shared dropbox path
   */
  async getSharedPath(): Promise<string | null> {
    const vaultRoot = await this.getVaultRoot()
    return vaultRoot ? `${vaultRoot}/${this.SHARED_FOLDER_NAME}` : null
  }
}

export const sharedDropboxService = new SharedDropboxService()
